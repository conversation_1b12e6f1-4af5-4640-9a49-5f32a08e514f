import React, { useState, useMemo, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import {
  Search,
  Filter,
  Clock,
  Loader2,
  AlertCircle,
  BarChart3,
  FileText,
  X,
  ChevronRight,
  Heart,
  Plus,
  Menu,
  Mail,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import Navbar from '@/components/Navbar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TimezoneSelector } from '@/components/ui/timezone-selector';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { ContentPagination } from '@/components/ui/content-pagination';
import ContentGenerationDialog from '@/components/ContentGenerationDialog';
import { EmailSubscriptionDialog } from '@/components/EmailSubscriptionDialog';
import { useContentSummaryData, ContentFilters } from '@/hooks/usePosts';
import { useTopics } from '@/hooks/useTopics';
import { useAuth, useSubscriptionPermission } from '@/contexts/AuthContext';
import { useFavoriteDatasources } from '@/hooks/useFavoriteDatasources';
import { useFavoriteSummaries } from '@/hooks/useFavoriteSummaries';
import { useTimezone } from '@/hooks/useTimezone';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTranslation } from 'react-i18next';
import { useIsMobile } from '@/hooks/use-mobile';
import { useToast } from '@/hooks/use-toast';
import { Sheet, SheetContent, SheetTrigger, SheetTitle, SheetHeader } from '@/components/ui/sheet';
import { isDateInRange, convertFromUTC } from '@/lib/timezone-utils';
import { DateRange } from 'react-day-picker';

import { SourceSubmissionDialog } from '@/components/SourceSubmissionDialog';
import PlatformMultiSelect from '@/components/PlatformMultiSelect';
import CollapsibleSummaryCard from '@/components/CollapsibleSummaryCard';
import HierarchicalDataSourceList from '@/components/HierarchicalDataSourceList';

const platformNames = {
  'reddit': 'Reddit',
  'twitter-rss': 'Twitter RSS',
  'blog': 'Blog',
  'wechat': 'Wechat',
  'xiaohongshu': 'Rednote',
  'youtube': 'YouTube',
  'podcast': 'Podcast',
  'twitter': 'Twitter'
};

interface DataSourceItem {
  id: string;
  name: string;
  platform: string;
  topic_id: string; // 主题ID
  topicName?: string; // 主题名称用于显示
  language?: 'EN' | 'ZH'; // 数据源语言
  summaryCount: number;
  lastUpdated?: string;
  last_crawled_at?: string;
}

// summary_type 映射
const getSummaryTypeForPlatform = (platform: string) => {
  switch (platform) {
    case 'blog': return 'blog_post';
    case 'youtube': return 'youtube_video';
    case 'podcast': return 'podcast';
    case 'xiaohongshu': return 'xiaohongshu_post';
    case 'reddit': return 'daily_subreddit';
    case 'twitter-rss': return 'twitter_rss_datasource';
    case 'wechat': return 'wechat_post';
    default: return null;
  }
};

const ContentSummaryLeftRight = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const { hasPermission: hasDateFilterPermission, isFree } = useSubscriptionPermission('date_filter');
  const { language } = useLanguage();
  const { t } = useTranslation();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeSearchQuery, setActiveSearchQuery] = useState(''); // 实际用于API调用的搜索查询
  const [selectedTopic, setSelectedTopic] = useState<string>('all');
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(['all']);
  const [selectedDateRange, setSelectedDateRange] = useState<DateRange | undefined>();
  const [selectedDataSource, setSelectedDataSource] = useState<string>('all');
  const { timezone: userTimezone, setTimezone: setUserTimezone, isLoaded: timezoneLoaded } = useTimezone();
  const [selectedTimezone, setSelectedTimezone] = useState(userTimezone);
  const [isFromURLParams, setIsFromURLParams] = useState(false); // 标记是否来自URL参数
  const [isInitialized, setIsInitialized] = useState(false); // 标记URL参数是否已解析完成
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  const [showFavoriteSummariesOnly, setShowFavoriteSummariesOnly] = useState(false);

  // Filter collapse state
  const [isFiltersCollapsed, setIsFiltersCollapsed] = useState(false);

  // Content generation dialog states
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedSummaryForGeneration, setSelectedSummaryForGeneration] = useState<any>(null);

  // 计算免费用户允许的日期范围（今天和昨天）
  const getFreeUserAllowedDateRange = () => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    return {
      from: yesterday,
      to: today
    };
  };

  // Source submission dialog state
  const [sourceSubmissionDialogOpen, setSourceSubmissionDialogOpen] = useState(false);

  // Email subscription dialog state
  const [emailSubscriptionDialogOpen, setEmailSubscriptionDialogOpen] = useState(false);

  // Mobile states
  const [mobileDataSourcesOpen, setMobileDataSourcesOpen] = useState(false);

  // Pagination states
  const [summariesPage, setSummariesPage] = useState(1);
  const [summariesPageSize, setSummariesPageSize] = useState(25);

  // Favorites hooks
  const { toggleFavorite, isFavorite, loading: favoritesLoading, favoriteDetails } = useFavoriteDatasources();
  const {
    toggleFavorite: toggleSummaryFavorite,
    isFavorite: isSummaryFavorite,
    loading: summaryFavoritesLoading
  } = useFavoriteSummaries();

  // 同步用户时区偏好
  useEffect(() => {
    if (timezoneLoaded) {
      setSelectedTimezone(userTimezone);
    }
  }, [userTimezone, timezoneLoaded]);

  // 从URL参数中读取筛选条件
  useEffect(() => {
    const topicParam = searchParams.get('topic');
    const dateParam = searchParams.get('date'); // 单日参数
    const dateFromParam = searchParams.get('dateFrom'); // 兼容旧的范围参数
    const dateToParam = searchParams.get('dateTo');
    const timezoneParam = searchParams.get('timezone');

    if (topicParam) {
      setSelectedTopic(topicParam);
    }

    // 优先处理单日参数
    if (dateParam) {
      // 从URL参数解析日期时，需要将UTC时间转换回用户时区的日期
      const utcDate = new Date(dateParam);
      const userTimezoneToUse = timezoneParam || (timezoneLoaded ? selectedTimezone : 'America/Los_Angeles');

      // 将UTC时间转换为用户时区的时间，然后取日期部分
      const userTimezoneDate = convertFromUTC(utcDate, userTimezoneToUse);
      const localDate = new Date(userTimezoneDate.getFullYear(), userTimezoneDate.getMonth(), userTimezoneDate.getDate());

      const dateRange: DateRange = {
        from: localDate,
        to: localDate  // 设置为相同的日期，显示为单日格式
      };
      setSelectedDateRange(dateRange);
      setIsFromURLParams(false); // 需要进行时区转换
    } else if (dateFromParam && dateToParam) {
      // 兼容旧的范围参数
      const dateRange: DateRange = {
        from: new Date(dateFromParam),
        to: new Date(dateToParam)
      };
      setSelectedDateRange(dateRange);
      setIsFromURLParams(false); // 需要进行时区转换
    } else {
      setIsFromURLParams(false);
    }

    if (timezoneParam) {
      setSelectedTimezone(timezoneParam);
    }

    // 标记URL参数解析完成 - 不再依赖timezoneLoaded
    setIsInitialized(true);
  }, [searchParams, timezoneLoaded]); // 移除selectedTimezone依赖，避免无限循环

  // 处理搜索提交
  const handleSearchSubmit = () => {
    setActiveSearchQuery(searchQuery);
    setSummariesPage(1);
  };

  // 处理回车键搜索
  const handleSearchKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearchSubmit();
    }
  };

  // 处理时区变更
  const handleTimezoneChange = (newTimezone: string) => {
    setSelectedTimezone(newTimezone);
    setUserTimezone(newTimezone); // 同时更新全局偏好
  };

  // Reset pagination when filters change
  useEffect(() => {
    setSummariesPage(1);
  }, [selectedTopic, selectedPlatforms, selectedDateRange, selectedTimezone, activeSearchQuery, selectedDataSource, showFavoriteSummariesOnly, showFavoritesOnly]);

  const { topics } = useTopics();

  // Build filters object
  const filters: ContentFilters = useMemo(() => {
    const result: ContentFilters = {};

    if (activeSearchQuery) result.searchQuery = activeSearchQuery;
    if (selectedTopic !== 'all') result.selectedTopic = selectedTopic;
    if (selectedPlatforms.length > 0 && !selectedPlatforms.includes('all')) {
      result.selectedPlatforms = selectedPlatforms;
    }
    if (selectedDateRange) {
      result.selectedDateRange = selectedDateRange;
      result.selectedTimezone = selectedTimezone;
      result.isDateRangeUTC = isFromURLParams; // 使用状态变量
    }

    // 添加收藏过滤状态到filters中，这样会触发数据重新获取
    result.showFavoritesOnly = showFavoritesOnly;
    result.showFavoriteSummariesOnly = showFavoriteSummariesOnly;

    return result;
  }, [activeSearchQuery, selectedTopic, selectedPlatforms, selectedDateRange, selectedTimezone, isFromURLParams, showFavoritesOnly, showFavoriteSummariesOnly]);

  // 使用统一的数据获取hook，只有在初始化完成后才开始查询
  const {
    summaries,
    allFilteredSummaries,
    dataSources,
    totalSummariesCount,
    filteredSummariesCount,
    totalDataSourcesCount,
    filteredDataSourcesCount,
    hasMore: summariesHasMore,
    loading: dataLoading,
    error: dataError
  } = useContentSummaryData(summariesPage, summariesPageSize, filters, isInitialized);

  // 应用前端特有的过滤（收藏筛选和数据源名称搜索）
  const filteredDataSources = useMemo(() => {
    return dataSources.filter(source => {
      // 数据源名称搜索筛选（与内容搜索分开）
      if (searchQuery && !source.name.toLowerCase().includes(searchQuery.toLowerCase())) return false;

      // 收藏筛选
      if (showFavoritesOnly && !isFavorite(source.id)) return false;

      // 当启用"仅显示收藏摘要"时，只显示有收藏摘要的数据源
      if (showFavoriteSummariesOnly) {
        const summaryType = getSummaryTypeForPlatform(source.platform);
        const hasAnyFavoriteSummary = allFilteredSummaries.some(summary =>
          summary.summary_type === summaryType &&
          summary.metadata?.source_name === source.name &&
          isSummaryFavorite(summary.id)
        );
        if (!hasAnyFavoriteSummary) return false;
      }

      return true;
    });
  }, [dataSources, searchQuery, showFavoritesOnly, isFavorite, showFavoriteSummariesOnly, allFilteredSummaries, isSummaryFavorite]);

  // Helper function to get platform from summary_type
  const getPlatformFromSummaryType = (summaryType: string): string => {
    const typeMap: { [key: string]: string } = {
      'blog_post': 'blog',
      'daily_subreddit': 'reddit',
      'youtube_video': 'youtube',
      'podcast': 'podcast',
      'wechat_post': 'wechat',
      'twitter_rss_datasource': 'twitter-rss',
      'xiaohongshu_post': 'xiaohongshu'
    };
    return typeMap[summaryType] || 'unknown';
  };

  // Helper function to get title from summary metadata
  const getTitleFromMetadata = (metadata: any, url: string, index: number = 0): string => {
    // Try different title fields based on the data structure
    if (metadata?.post_titles && Array.isArray(metadata.post_titles) && metadata.post_titles[index]) {
      return metadata.post_titles[index];
    }
    if (metadata?.post_title) {
      return metadata.post_title;
    }
    if (metadata?.episode_title) {
      return metadata.episode_title;
    }
    if (metadata?.video_title) {
      return metadata.video_title;
    }
    // Fallback to URL
    return url;
  };

  // 根据选中的数据源过滤内容 (其他筛选已在数据库层面完成)
  const filteredContent = useMemo(() => {
    let baseSummaries: any[] = [];

    if (selectedDataSource === 'all') {
      // 显示所有数据源时，使用分页后的摘要
      baseSummaries = summaries;
    } else {
      // 显示特定数据源时，从所有过滤后的摘要中查找
      const selectedSource = dataSources.find(ds => ds.id === selectedDataSource);
      if (selectedSource) {
        const summaryType = getSummaryTypeForPlatform(selectedSource.platform);
        // Filter by summary_type and source_name from all filtered summaries
        baseSummaries = allFilteredSummaries.filter(s =>
          s.summary_type === summaryType &&
          s.metadata?.source_name === selectedSource.name
        );
      }
    }

    // 应用收藏摘要过滤
    if (showFavoriteSummariesOnly) {
      baseSummaries = baseSummaries.filter(summary => isSummaryFavorite(summary.id));
    }

    // 当启用"仅显示收藏数据源"时，只显示来自收藏数据源的摘要
    if (showFavoritesOnly) {
      baseSummaries = baseSummaries.filter(summary => {
        // 找到摘要对应的数据源
        const summaryType = summary.summary_type;
        const sourceName = summary.metadata?.source_name;
        const correspondingDataSource = dataSources.find(ds => {
          const dsType = getSummaryTypeForPlatform(ds.platform);
          return dsType === summaryType && ds.name === sourceName;
        });

        // 只保留来自收藏数据源的摘要
        return correspondingDataSource && isFavorite(correspondingDataSource.id);
      });
    }

    return { summaries: baseSummaries };
  }, [summaries, allFilteredSummaries, selectedDataSource, dataSources, showFavoriteSummariesOnly, isSummaryFavorite, showFavoritesOnly, isFavorite]);

  const stats = useMemo(() => {
    return {
      totalSummaries: totalSummariesCount,
      filteredSummariesCount: filteredSummariesCount,
      totalDataSources: totalDataSourcesCount,
      filteredDataSources: filteredDataSourcesCount
    };
  }, [totalSummariesCount, filteredSummariesCount, totalDataSourcesCount, filteredDataSourcesCount]);

  // 处理生成内容按钮点击
  const handleGenerateContent = (summary: any) => {
    if (!isAuthenticated) {
      // 如果用户未登录，跳转到登录页面
      navigate('/auth');
      return;
    }

    // 设置选中的摘要并打开弹窗
    setSelectedSummaryForGeneration({
      id: summary.id,
      content: summary.content,
      platform: summary.platform,
      source_name: summary.source_name,
      source_urls: summary.source_urls || [],
      topic_name: summary.topic_name
    });
    setDialogOpen(true);
  };

  const loading = dataLoading;
  const error = dataError;

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="flex items-center justify-center h-96">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">加载失败</h2>
              <p className="text-muted-foreground">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background content-summary-enlarged">
      <Navbar />

      <div className="max-w-7xl mx-auto mobile-content-area mobile-ultra-compact sm:px-6 lg:px-8 mobile-page-padding py-8">
        {/* Header - Title, Date, Stats and Action Buttons */}
        <div className="flex flex-col gap-4 mb-8">
          {/* Top row - Title, Date and Stats Cards */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            {/* Left side - Title */}
            <div className="flex flex-col gap-1">
              <h1 className="text-2xl mobile-text-base md:text-3xl lg:text-4xl font-bold mobile-title">
                {t('contentSummary.title')}
              </h1>
            </div>

            {/* Right side - Stats Cards */}
            <div className="flex flex-wrap gap-3 sm:gap-4">
              {/* Summaries Card */}
              <div className="flex items-center gap-2 px-3 py-2 bg-purple-50 rounded-lg min-w-0 flex-1 sm:flex-none">
                <div className="p-1 bg-purple-100 rounded">
                  <FileText className="h-4 w-4 text-purple-600" />
                </div>
                <div className="text-center min-w-0">
                  <div className="text-lg font-bold text-purple-600 truncate">
                    {stats.filteredSummariesCount.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-600 truncate">{t('contentSummary.stats.summaries')}</div>
                </div>
              </div>

              {/* Data Sources Card */}
              <div className="flex items-center gap-2 px-3 py-2 bg-orange-50 rounded-lg min-w-0 flex-1 sm:flex-none">
                <div className="p-1 bg-orange-100 rounded">
                  <BarChart3 className="h-4 w-4 text-orange-600" />
                </div>
                <div className="text-center min-w-0">
                  <div className="text-lg font-bold text-orange-600 truncate">
                    {stats.filteredDataSources}
                  </div>
                  <div className="text-xs text-gray-600 truncate">{t('contentSummary.stats.dataSources')}</div>
                </div>
              </div>

              {/* Pages Card */}
              <div className="flex items-center gap-2 px-3 py-2 bg-blue-50 rounded-lg min-w-0 flex-1 sm:flex-none">
                <div className="p-1 bg-blue-100 rounded">
                  <Clock className="h-4 w-4 text-blue-600" />
                </div>
                <div className="text-center min-w-0">
                  <div className="text-lg font-bold text-blue-600 truncate">
                    {Math.ceil(totalSummariesCount / summariesPageSize).toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-600 truncate">{language === 'zh' ? '页数' : 'Pages'}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom row - Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={() => setEmailSubscriptionDialogOpen(true)}
              className="flex items-center gap-2 bg-purple-600 hover:bg-purple-700 text-white"
              size="sm"
            >
              <Plus className="h-4 w-4" />
              {t('emailSubscription.subscribe')}
            </Button>
            <Button
              onClick={() => setSourceSubmissionDialogOpen(true)}
              className="flex items-center gap-2"
              variant="outline"
              size="sm"
            >
              <Mail className="h-4 w-4" />
              {t('sourceSubmission.button')}
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card className="mobile-title mobile-content-ultra mobile-border-expand mb-6 card-transparent">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-medium">{t('contentSummary.filters.title')}</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsFiltersCollapsed(!isFiltersCollapsed)}
                className="h-8 w-8 p-0"
              >
                {isFiltersCollapsed ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronUp className="h-4 w-4" />
                )}
              </Button>
            </div>
          </CardHeader>

          {!isFiltersCollapsed && (
            <CardContent className="mobile-filters mobile-content-ultra pt-0">
              <div className="mobile-form-spacing space-y-4">
                {/* 搜索框 */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder={t('contentSummary.filters.searchPlaceholder')}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={handleSearchKeyPress}
                    className="pl-10 pr-20"
                  />
                  <Button
                    onClick={handleSearchSubmit}
                    size="sm"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 bg-orange-500 hover:bg-orange-600"
                  >
                    {t('contentSummary.filters.search')}
                  </Button>
                </div>

                {/* 筛选器行 */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <Label className="text-sm text-muted-foreground mb-2 block">{t('contentSummary.filters.topic')}</Label>
                    <Select value={selectedTopic} onValueChange={setSelectedTopic}>
                      <SelectTrigger>
                        <SelectValue placeholder={t('contentSummary.filters.allTopics')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">{t('contentSummary.filters.allTopics')}</SelectItem>
                        {topics.map(topic => (
                          <SelectItem key={topic.id} value={topic.id}>
                            {topic.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label className="text-sm text-muted-foreground mb-2 block">{t('contentSummary.filters.platform')}</Label>
                    <PlatformMultiSelect
                      selectedPlatforms={selectedPlatforms}
                      onPlatformChange={setSelectedPlatforms}
                      placeholder={t('contentSummary.filters.allPlatforms')}
                      allPlatformsText={t('contentSummary.filters.allPlatforms')}
                    />
                  </div>

                  <div>
                    <Label className="text-sm text-muted-foreground mb-2 block">{t('contentSummary.filters.timezone')}</Label>
                    <TimezoneSelector
                      value={selectedTimezone}
                      onValueChange={handleTimezoneChange}
                    />
                  </div>

                  <div>
                    <Label className="text-sm text-muted-foreground mb-2 block">{t('contentSummary.filters.dateRange')}</Label>
                    <DateRangePicker
                      value={selectedDateRange}
                      onValueChange={(dateRange) => {
                        // 如果是免费用户且没有日期过滤权限，限制日期范围
                        if (isFree && !hasDateFilterPermission && dateRange) {
                          const allowedRange = getFreeUserAllowedDateRange();

                          // 检查选择的日期是否在允许范围内
                          const isFromValid = !dateRange.from || dateRange.from >= allowedRange.from;
                          const isToValid = !dateRange.to || dateRange.to <= allowedRange.to;

                          if (!isFromValid || !isToValid) {
                            // 显示限制提示
                            toast({
                              title: t('common.freeLimitReached'),
                              description: t('common.freeLimitDesc'),
                              variant: 'destructive',
                            });
                            return;
                          }
                        }
                        setSelectedDateRange(dateRange);
                      }}
                      placeholder={t('contentSummary.filters.custom')}
                      disabled={isFree && !hasDateFilterPermission ? {
                        before: getFreeUserAllowedDateRange().from,
                        after: getFreeUserAllowedDateRange().to
                      } : undefined}
                    />
                  </div>
                </div>

                {/* 底部筛选选项 */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pt-2 border-t">
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="favorites-only"
                        checked={showFavoritesOnly}
                        onCheckedChange={setShowFavoritesOnly}
                      />
                      <Label htmlFor="favorites-only" className="text-sm">
                        {t('contentSummary.favorites.showFavoriteDataSources')}
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="favorite-summaries-only"
                        checked={showFavoriteSummariesOnly}
                        onCheckedChange={setShowFavoriteSummariesOnly}
                      />
                      <Label htmlFor="favorite-summaries-only" className="text-sm">
                        {t('contentSummary.favorites.showFavoriteSummaries')}
                      </Label>
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSearchQuery('');
                      setActiveSearchQuery('');
                      setSelectedTopic('all');
                      setSelectedPlatforms(['all']);
                      setSelectedDateRange(undefined);
                      setSelectedTimezone(userTimezone); // 重置为用户偏好时区
                      setSelectedDataSource('all');
                      setShowFavoritesOnly(false);
                      setShowFavoriteSummariesOnly(false);
                    }}
                    className="flex items-center gap-2"
                  >
                    <X className="h-4 w-4" />
                    {t('contentSummary.filters.clearFilters')}
                  </Button>
                </div>
              </div>
            </CardContent>
          )}
        </Card>

        {/* Main Content - Left Right Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-2 md:gap-6">
          {/* Left Sidebar - Data Sources - Desktop */}
          <div className="hidden lg:block lg:col-span-1">
            <Card className="card-subtle">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  {t('contentSummary.dataSourceList')} ({filteredDataSources.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <HierarchicalDataSourceList
                  dataSources={filteredDataSources}
                  selectedDataSource={selectedDataSource}
                  onDataSourceSelect={setSelectedDataSource}
                  isFavorite={isFavorite}
                  toggleFavorite={toggleFavorite}
                  favoritesLoading={favoritesLoading}
                  totalSummariesCount={stats.filteredSummariesCount}
                />
              </CardContent>
            </Card>
          </div>

          {/* Right Content Area */}
          <div className="lg:col-span-3">
            {/* Mobile Data Sources Button */}
            <div className="lg:hidden mb-4">
              <Sheet open={mobileDataSourcesOpen} onOpenChange={setMobileDataSourcesOpen}>
                <SheetTrigger asChild>
                  <Button variant="outline" className="w-full justify-start">
                    <Menu className="h-4 w-4 mr-2" />
                    {selectedDataSource === 'all'
                      ? t('contentSummary.allDataSources')
                      : filteredDataSources.find(s => s.id === selectedDataSource)?.name || t('contentSummary.dataSource')
                    }
                    <Badge variant="secondary" className="ml-auto">
                      {filteredDataSources.length}
                    </Badge>
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="w-[300px] sm:w-[400px]">
                  <SheetHeader>
                    <SheetTitle>{t('contentSummary.dataSourceList')} ({filteredDataSources.length})</SheetTitle>
                  </SheetHeader>
                  <div className="space-y-4 mt-6">
                    <HierarchicalDataSourceList
                      dataSources={filteredDataSources}
                      selectedDataSource={selectedDataSource}
                      onDataSourceSelect={setSelectedDataSource}
                      isFavorite={isFavorite}
                      toggleFavorite={toggleFavorite}
                      favoritesLoading={favoritesLoading}
                      totalSummariesCount={totalSummariesCount}
                      isMobile={true}
                      onMobileClose={() => setMobileDataSourcesOpen(false)}
                    />
                  </div>
                </SheetContent>
              </Sheet>
            </div>

            <Card className="mobile-content-ultra mobile-border-expand card-transparent">
              <CardHeader className="mobile-content-ultra">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-blue-500" />
                    <span className="truncate">
                      {selectedDataSource === 'all'
                        ? t('contentSummary.allSummaries')
                        : `${filteredDataSources.find(s => s.id === selectedDataSource)?.name || t('contentSummary.dataSource')} - ${t('contentSummary.summaries')}`
                      }
                    </span>
                  </div>
                  <Badge variant="outline">
                    {filteredContent.summaries.length} {t('contentSummary.itemsCount')}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="mobile-content-ultra">
                <div className="space-y-4">
                  {/* Content List */}
                  <div className="mobile-card-spacing space-y-4">
                    {filteredContent.summaries.length > 0 ? (
                      filteredContent.summaries.map(summary => (
                        <CollapsibleSummaryCard
                          key={summary.id}
                          summary={summary}
                          platformNames={platformNames}
                          getPlatformFromSummaryType={getPlatformFromSummaryType}
                          getTitleFromMetadata={getTitleFromMetadata}
                          isFavorite={isFavorite}
                          toggleFavorite={toggleFavorite}
                          isSummaryFavorite={isSummaryFavorite}
                          toggleSummaryFavorite={toggleSummaryFavorite}
                          handleGenerateContent={handleGenerateContent}
                          favoritesLoading={favoritesLoading}
                          summaryFavoritesLoading={summaryFavoritesLoading}
                        />
                      ))
                    ) : (
                      <div className="text-center py-12">
                        <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-semibold mb-2">{t('contentSummary.empty.title')}</h3>
                        <p className="text-muted-foreground">
                          {selectedDataSource === 'all'
                            ? t('contentSummary.empty.description')
                            : t('contentSummary.empty.noDataSourceSummaries', 'This data source has no summaries')
                          }
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Pagination */}
                  {filteredContent.summaries.length > 0 && (
                    <div className="border-t pt-4">
                      <ContentPagination
                        currentPage={summariesPage}
                        totalPages={Math.ceil(totalSummariesCount / summariesPageSize)}
                        totalItems={totalSummariesCount}
                        itemsPerPage={summariesPageSize}
                        onPageChange={setSummariesPage}
                        loading={dataLoading}
                      />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Content Generation Dialog */}
      {selectedSummaryForGeneration && (
        <ContentGenerationDialog
          open={dialogOpen}
          onOpenChange={setDialogOpen}
          summaryData={selectedSummaryForGeneration}
        />
      )}

      {/* Source Submission Dialog */}
      <SourceSubmissionDialog
        open={sourceSubmissionDialogOpen}
        onOpenChange={setSourceSubmissionDialogOpen}
      />

      {/* Email Subscription Dialog */}
      <EmailSubscriptionDialog
        open={emailSubscriptionDialogOpen}
        onOpenChange={setEmailSubscriptionDialogOpen}
      />
    </div>
  );
};

export default ContentSummaryLeftRight;
